<?php

use App\Http\Controllers\Api\PortfolioApiController;
use App\Http\Controllers\Portfolio\CertificationController;
use App\Http\Controllers\Portfolio\EducationController;
use App\Http\Controllers\Portfolio\ExperienceController;
use App\Http\Controllers\Portfolio\PortfolioSettingController;
use App\Http\Controllers\Portfolio\ProjectController;
use App\Http\Controllers\Portfolio\SkillController;
use App\Http\Controllers\Portfolio\SocialLinkController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    // Portfolio Management Routes
    Route::prefix('portfolio')->name('portfolio.')->group(function () {
        Route::get('settings', [PortfolioSettingController::class, 'index'])->name('settings.index');
        Route::post('settings', [PortfolioSettingController::class, 'store'])->name('settings.store');

        Route::resource('experiences', ExperienceController::class)->except(['show', 'create', 'edit']);
        Route::resource('skills', SkillController::class)->except(['show', 'create', 'edit']);
        Route::resource('projects', ProjectController::class)->except(['show', 'create', 'edit']);
        Route::resource('social-links', SocialLinkController::class)->except(['show', 'create', 'edit']);
        Route::resource('certifications', CertificationController::class)->except(['show', 'create', 'edit']);
        Route::resource('education', EducationController::class)->except(['show', 'create', 'edit']);
    });
});

// API Routes for Portfolio Data
Route::prefix('api')->group(function () {
    Route::get('portfolio', [PortfolioApiController::class, 'getPortfolioData'])->name('api.portfolio');
    Route::get('portfolio/user/{user}', [PortfolioApiController::class, 'getUserPortfolioData'])->name('api.portfolio.user');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
