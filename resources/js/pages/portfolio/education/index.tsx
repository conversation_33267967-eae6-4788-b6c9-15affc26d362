import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useForm } from '@inertiajs/react';
import { FormEventHandler, useState } from 'react';
import { Plus, Edit, Trash2, GraduationCap } from 'lucide-react';
import { toast } from 'sonner';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';

interface Education {
    id: number;
    degree: string;
    institution: string;
    year: string;
    logo?: string;
    description?: string;
    sort_order: number;
}

interface Props {
    education: Education[];
}

export default function EducationPage({ education }: Props) {
    const [editingEducation, setEditingEducation] = useState<Education | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    const { data, setData, post, put, delete: destroy, processing, errors, reset } = useForm({
        degree: '',
        institution: '',
        year: '',
        logo: '',
        description: '',
        sort_order: 0,
    });

    const openCreateDialog = () => {
        reset();
        setEditingEducation(null);
        setIsDialogOpen(true);
    };

    const openEditDialog = (education: Education) => {
        setData({
            degree: education.degree,
            institution: education.institution,
            year: education.year,
            logo: education.logo || '',
            description: education.description || '',
            sort_order: education.sort_order,
        });
        setEditingEducation(education);
        setIsDialogOpen(true);
    };

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        
        if (editingEducation) {
            put(route('portfolio.education.update', editingEducation.id), {
                onSuccess: () => {
                    toast.success('Education updated successfully!');
                    setIsDialogOpen(false);
                },
                onError: () => {
                    toast.error('Failed to update education.');
                },
            });
        } else {
            post(route('portfolio.education.store'), {
                onSuccess: () => {
                    toast.success('Education added successfully!');
                    setIsDialogOpen(false);
                },
                onError: () => {
                    toast.error('Failed to add education.');
                },
            });
        }
    };

    const deleteEducation = (education: Education) => {
        if (confirm('Are you sure you want to delete this education record?')) {
            destroy(route('portfolio.education.destroy', education.id), {
                onSuccess: () => {
                    toast.success('Education deleted successfully!');
                },
                onError: () => {
                    toast.error('Failed to delete education.');
                },
            });
        }
    };

    return (
        <AppLayout title="Education Management">
            <div className="space-y-6">
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-3xl font-bold">Education Management</h1>
                        <p className="text-muted-foreground">
                            Manage your educational background and qualifications.
                        </p>
                    </div>
                    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                        <DialogTrigger asChild>
                            <Button onClick={openCreateDialog}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Education
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>
                                    {editingEducation ? 'Edit Education' : 'Add New Education'}
                                </DialogTitle>
                                <DialogDescription>
                                    {editingEducation 
                                        ? 'Update your education details.' 
                                        : 'Add a new educational qualification or degree.'
                                    }
                                </DialogDescription>
                            </DialogHeader>
                            
                            <form onSubmit={submit} className="space-y-4">
                                <div>
                                    <Label htmlFor="degree">Degree/Qualification *</Label>
                                    <Input
                                        id="degree"
                                        value={data.degree}
                                        onChange={(e) => setData('degree', e.target.value)}
                                        placeholder="e.g., Bachelor of Computer Science"
                                        required
                                    />
                                    {errors.degree && (
                                        <p className="text-sm text-destructive">{errors.degree}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="institution">Institution *</Label>
                                    <Input
                                        id="institution"
                                        value={data.institution}
                                        onChange={(e) => setData('institution', e.target.value)}
                                        placeholder="e.g., University of Technology"
                                        required
                                    />
                                    {errors.institution && (
                                        <p className="text-sm text-destructive">{errors.institution}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="year">Year/Period *</Label>
                                    <Input
                                        id="year"
                                        value={data.year}
                                        onChange={(e) => setData('year', e.target.value)}
                                        placeholder="e.g., 2018-2022, 2020"
                                        required
                                    />
                                    {errors.year && (
                                        <p className="text-sm text-destructive">{errors.year}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="logo">Institution Logo URL</Label>
                                    <Input
                                        id="logo"
                                        value={data.logo}
                                        onChange={(e) => setData('logo', e.target.value)}
                                        placeholder="https://example.com/logo.png"
                                    />
                                    {errors.logo && (
                                        <p className="text-sm text-destructive">{errors.logo}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="description">Description</Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        placeholder="Additional details about your education..."
                                    />
                                    {errors.description && (
                                        <p className="text-sm text-destructive">{errors.description}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="sort_order">Sort Order</Label>
                                    <Input
                                        id="sort_order"
                                        type="number"
                                        value={data.sort_order}
                                        onChange={(e) => setData('sort_order', parseInt(e.target.value) || 0)}
                                        min="0"
                                    />
                                </div>

                                <div className="flex justify-end gap-2">
                                    <Button 
                                        type="button" 
                                        variant="outline" 
                                        onClick={() => setIsDialogOpen(false)}
                                    >
                                        Cancel
                                    </Button>
                                    <Button type="submit" disabled={processing}>
                                        {processing ? 'Saving...' : editingEducation ? 'Update' : 'Create'}
                                    </Button>
                                </div>
                            </form>
                        </DialogContent>
                    </Dialog>
                </div>

                <div className="grid gap-4">
                    {education.map((edu) => (
                        <Card key={edu.id}>
                            <CardHeader>
                                <div className="flex justify-between items-start">
                                    <div className="flex items-start gap-3">
                                        <div className="p-2 bg-indigo-500/10 rounded-lg">
                                            <GraduationCap className="h-5 w-5 text-indigo-600" />
                                        </div>
                                        <div>
                                            <CardTitle className="text-lg">{edu.degree}</CardTitle>
                                            <CardDescription>
                                                {edu.institution} • {edu.year}
                                            </CardDescription>
                                            {edu.description && (
                                                <p className="text-sm text-muted-foreground mt-2">
                                                    {edu.description}
                                                </p>
                                            )}
                                        </div>
                                    </div>
                                    <div className="flex gap-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => openEditDialog(edu)}
                                        >
                                            <Edit className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => deleteEducation(edu)}
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            </CardHeader>
                        </Card>
                    ))}
                </div>

                {education.length === 0 && (
                    <Card>
                        <CardContent className="text-center py-8">
                            <p className="text-muted-foreground">
                                No education records added yet. Click "Add Education" to get started.
                            </p>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
