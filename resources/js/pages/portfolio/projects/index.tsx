import AppLayout from '@/layouts/app-layout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useForm } from '@inertiajs/react';
import { FormEventHandler, useState } from 'react';
import { X, Plus, Edit, Trash2, ExternalLink, Github } from 'lucide-react';
import { toast } from 'sonner';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';

interface Project {
    id: number;
    title: string;
    description: string;
    image?: string;
    url?: string;
    github_url?: string;
    technologies?: string[];
    status: 'completed' | 'in_progress' | 'planned';
    featured: boolean;
    sort_order: number;
}

interface Props {
    projects: Project[];
}

const statusLabels = {
    completed: 'Completed',
    in_progress: 'In Progress',
    planned: 'Planned',
};

const statusColors = {
    completed: 'bg-green-500',
    in_progress: 'bg-blue-500',
    planned: 'bg-gray-500',
};

export default function ProjectsPage({ projects }: Props) {
    const [editingProject, setEditingProject] = useState<Project | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    const { data, setData, post, put, delete: destroy, processing, errors, reset } = useForm({
        title: '',
        description: '',
        image: '',
        url: '',
        github_url: '',
        technologies: [] as string[],
        status: 'completed' as Project['status'],
        featured: false,
        sort_order: 0,
    });

    const [newTechnology, setNewTechnology] = useState('');

    const openCreateDialog = () => {
        reset();
        setEditingProject(null);
        setIsDialogOpen(true);
    };

    const openEditDialog = (project: Project) => {
        setData({
            title: project.title,
            description: project.description,
            image: project.image || '',
            url: project.url || '',
            github_url: project.github_url || '',
            technologies: project.technologies || [],
            status: project.status,
            featured: project.featured,
            sort_order: project.sort_order,
        });
        setEditingProject(project);
        setIsDialogOpen(true);
    };

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        
        if (editingProject) {
            put(route('portfolio.projects.update', editingProject.id), {
                onSuccess: () => {
                    toast.success('Project updated successfully!');
                    setIsDialogOpen(false);
                },
                onError: () => {
                    toast.error('Failed to update project.');
                },
            });
        } else {
            post(route('portfolio.projects.store'), {
                onSuccess: () => {
                    toast.success('Project added successfully!');
                    setIsDialogOpen(false);
                },
                onError: () => {
                    toast.error('Failed to add project.');
                },
            });
        }
    };

    const deleteProject = (project: Project) => {
        if (confirm('Are you sure you want to delete this project?')) {
            destroy(route('portfolio.projects.destroy', project.id), {
                onSuccess: () => {
                    toast.success('Project deleted successfully!');
                },
                onError: () => {
                    toast.error('Failed to delete project.');
                },
            });
        }
    };

    const addTechnology = () => {
        if (newTechnology.trim()) {
            setData('technologies', [...data.technologies, newTechnology.trim()]);
            setNewTechnology('');
        }
    };

    const removeTechnology = (index: number) => {
        setData('technologies', data.technologies.filter((_, i) => i !== index));
    };

    return (
        <AppLayout title="Projects Management">
            <div className="space-y-6">
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-3xl font-bold">Projects Management</h1>
                        <p className="text-muted-foreground">
                            Manage your portfolio projects and showcase your work.
                        </p>
                    </div>
                    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                        <DialogTrigger asChild>
                            <Button onClick={openCreateDialog}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Project
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                            <DialogHeader>
                                <DialogTitle>
                                    {editingProject ? 'Edit Project' : 'Add New Project'}
                                </DialogTitle>
                                <DialogDescription>
                                    {editingProject 
                                        ? 'Update your project details and information.' 
                                        : 'Add a new project to your portfolio.'
                                    }
                                </DialogDescription>
                            </DialogHeader>
                            
                            <form onSubmit={submit} className="space-y-4">
                                <div>
                                    <Label htmlFor="title">Project Title *</Label>
                                    <Input
                                        id="title"
                                        value={data.title}
                                        onChange={(e) => setData('title', e.target.value)}
                                        required
                                    />
                                    {errors.title && (
                                        <p className="text-sm text-destructive">{errors.title}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="description">Description *</Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        placeholder="Describe your project..."
                                        required
                                    />
                                    {errors.description && (
                                        <p className="text-sm text-destructive">{errors.description}</p>
                                    )}
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="url">Live URL</Label>
                                        <Input
                                            id="url"
                                            type="url"
                                            value={data.url}
                                            onChange={(e) => setData('url', e.target.value)}
                                            placeholder="https://example.com"
                                        />
                                        {errors.url && (
                                            <p className="text-sm text-destructive">{errors.url}</p>
                                        )}
                                    </div>
                                    <div>
                                        <Label htmlFor="github_url">GitHub URL</Label>
                                        <Input
                                            id="github_url"
                                            type="url"
                                            value={data.github_url}
                                            onChange={(e) => setData('github_url', e.target.value)}
                                            placeholder="https://github.com/username/repo"
                                        />
                                        {errors.github_url && (
                                            <p className="text-sm text-destructive">{errors.github_url}</p>
                                        )}
                                    </div>
                                </div>

                                <div>
                                    <Label htmlFor="image">Image URL</Label>
                                    <Input
                                        id="image"
                                        value={data.image}
                                        onChange={(e) => setData('image', e.target.value)}
                                        placeholder="https://example.com/image.jpg"
                                    />
                                    {errors.image && (
                                        <p className="text-sm text-destructive">{errors.image}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="status">Status</Label>
                                    <Select value={data.status} onValueChange={(value) => setData('status', value as Project['status'])}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select status" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="completed">Completed</SelectItem>
                                            <SelectItem value="in_progress">In Progress</SelectItem>
                                            <SelectItem value="planned">Planned</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.status && (
                                        <p className="text-sm text-destructive">{errors.status}</p>
                                    )}
                                </div>

                                {/* Technologies */}
                                <div>
                                    <Label>Technologies Used</Label>
                                    <div className="flex gap-2 mt-2">
                                        <Input
                                            value={newTechnology}
                                            onChange={(e) => setNewTechnology(e.target.value)}
                                            placeholder="Add a technology"
                                            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTechnology())}
                                        />
                                        <Button type="button" onClick={addTechnology} size="sm">
                                            <Plus className="h-4 w-4" />
                                        </Button>
                                    </div>
                                    <div className="flex flex-wrap gap-2 mt-2">
                                        {data.technologies.map((tech, index) => (
                                            <Badge key={index} variant="secondary" className="flex items-center gap-1">
                                                {tech}
                                                <button
                                                    type="button"
                                                    onClick={() => removeTechnology(index)}
                                                    className="ml-1 hover:text-destructive"
                                                >
                                                    <X className="h-3 w-3" />
                                                </button>
                                            </Badge>
                                        ))}
                                    </div>
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Switch
                                        id="featured"
                                        checked={data.featured}
                                        onCheckedChange={(checked) => setData('featured', checked)}
                                    />
                                    <Label htmlFor="featured">Featured project</Label>
                                </div>

                                <div>
                                    <Label htmlFor="sort_order">Sort Order</Label>
                                    <Input
                                        id="sort_order"
                                        type="number"
                                        value={data.sort_order}
                                        onChange={(e) => setData('sort_order', parseInt(e.target.value) || 0)}
                                        min="0"
                                    />
                                </div>

                                <div className="flex justify-end gap-2">
                                    <Button 
                                        type="button" 
                                        variant="outline" 
                                        onClick={() => setIsDialogOpen(false)}
                                    >
                                        Cancel
                                    </Button>
                                    <Button type="submit" disabled={processing}>
                                        {processing ? 'Saving...' : editingProject ? 'Update' : 'Create'}
                                    </Button>
                                </div>
                            </form>
                        </DialogContent>
                    </Dialog>
                </div>

                <div className="grid gap-4">
                    {projects.map((project) => (
                        <Card key={project.id}>
                            <CardHeader>
                                <div className="flex justify-between items-start">
                                    <div className="flex-1">
                                        <div className="flex items-center gap-2 mb-2">
                                            <CardTitle>{project.title}</CardTitle>
                                            {project.featured && (
                                                <Badge variant="default">Featured</Badge>
                                            )}
                                            <Badge 
                                                variant="secondary" 
                                                className={`text-white ${statusColors[project.status]}`}
                                            >
                                                {statusLabels[project.status]}
                                            </Badge>
                                        </div>
                                        <CardDescription>{project.description}</CardDescription>
                                    </div>
                                    <div className="flex gap-2">
                                        {project.url && (
                                            <Button variant="outline" size="sm" asChild>
                                                <a href={project.url} target="_blank" rel="noopener noreferrer">
                                                    <ExternalLink className="h-4 w-4" />
                                                </a>
                                            </Button>
                                        )}
                                        {project.github_url && (
                                            <Button variant="outline" size="sm" asChild>
                                                <a href={project.github_url} target="_blank" rel="noopener noreferrer">
                                                    <Github className="h-4 w-4" />
                                                </a>
                                            </Button>
                                        )}
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => openEditDialog(project)}
                                        >
                                            <Edit className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => deleteProject(project)}
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            </CardHeader>
                            {project.technologies && project.technologies.length > 0 && (
                                <CardContent>
                                    <div className="flex flex-wrap gap-1">
                                        {project.technologies.map((tech, index) => (
                                            <Badge key={index} variant="outline" className="text-xs">
                                                {tech}
                                            </Badge>
                                        ))}
                                    </div>
                                </CardContent>
                            )}
                        </Card>
                    ))}
                </div>

                {projects.length === 0 && (
                    <Card>
                        <CardContent className="text-center py-8">
                            <p className="text-muted-foreground">
                                No projects added yet. Click "Add Project" to get started.
                            </p>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
