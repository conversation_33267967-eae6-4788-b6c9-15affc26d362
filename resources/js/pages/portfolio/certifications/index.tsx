import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler, useState } from 'react';
import { Plus, Edit, Trash2, ExternalLink, Award } from 'lucide-react';
import { toast } from 'sonner';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';

interface Certification {
    id: number;
    name: string;
    issuer: string;
    date: string;
    logo?: string;
    credential_url?: string;
    sort_order: number;
}

interface Props {
    certifications: Certification[];
}

export default function CertificationsPage({ certifications }: Props) {
    const [editingCertification, setEditingCertification] = useState<Certification | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    const { data, setData, post, put, delete: destroy, processing, errors, reset } = useForm({
        name: '',
        issuer: '',
        date: '',
        logo: '',
        credential_url: '',
        sort_order: 0,
    });

    const openCreateDialog = () => {
        reset();
        setEditingCertification(null);
        setIsDialogOpen(true);
    };

    const openEditDialog = (certification: Certification) => {
        setData({
            name: certification.name,
            issuer: certification.issuer,
            date: certification.date,
            logo: certification.logo || '',
            credential_url: certification.credential_url || '',
            sort_order: certification.sort_order,
        });
        setEditingCertification(certification);
        setIsDialogOpen(true);
    };

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        if (editingCertification) {
            put(route('portfolio.certifications.update', editingCertification.id), {
                onSuccess: () => {
                    toast.success('Certification updated successfully!');
                    setIsDialogOpen(false);
                },
                onError: () => {
                    toast.error('Failed to update certification.');
                },
            });
        } else {
            post(route('portfolio.certifications.store'), {
                onSuccess: () => {
                    toast.success('Certification added successfully!');
                    setIsDialogOpen(false);
                },
                onError: () => {
                    toast.error('Failed to add certification.');
                },
            });
        }
    };

    const deleteCertification = (certification: Certification) => {
        if (confirm('Are you sure you want to delete this certification?')) {
            destroy(route('portfolio.certifications.destroy', certification.id), {
                onSuccess: () => {
                    toast.success('Certification deleted successfully!');
                },
                onError: () => {
                    toast.error('Failed to delete certification.');
                },
            });
        }
    };

    return (
        <AppLayout>
            <Head title="Certifications Management"/>
            <div className="space-y-6 p-6">
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-3xl font-bold">Certifications Management</h1>
                        <p className="text-muted-foreground">
                            Manage your professional certifications and credentials.
                        </p>
                    </div>
                    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                        <DialogTrigger asChild>
                            <Button onClick={openCreateDialog}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Certification
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>
                                    {editingCertification ? 'Edit Certification' : 'Add New Certification'}
                                </DialogTitle>
                                <DialogDescription>
                                    {editingCertification
                                        ? 'Update your certification details.'
                                        : 'Add a new professional certification or credential.'
                                    }
                                </DialogDescription>
                            </DialogHeader>

                            <form onSubmit={submit} className="space-y-4">
                                <div>
                                    <Label htmlFor="name">Certification Name *</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="e.g., AWS Certified Solutions Architect"
                                        required
                                    />
                                    {errors.name && (
                                        <p className="text-sm text-destructive">{errors.name}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="issuer">Issuing Organization *</Label>
                                    <Input
                                        id="issuer"
                                        value={data.issuer}
                                        onChange={(e) => setData('issuer', e.target.value)}
                                        placeholder="e.g., Amazon Web Services"
                                        required
                                    />
                                    {errors.issuer && (
                                        <p className="text-sm text-destructive">{errors.issuer}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="date">Date Obtained *</Label>
                                    <Input
                                        id="date"
                                        value={data.date}
                                        onChange={(e) => setData('date', e.target.value)}
                                        placeholder="e.g., 2023, March 2023"
                                        required
                                    />
                                    {errors.date && (
                                        <p className="text-sm text-destructive">{errors.date}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="logo">Logo URL</Label>
                                    <Input
                                        id="logo"
                                        value={data.logo}
                                        onChange={(e) => setData('logo', e.target.value)}
                                        placeholder="https://example.com/logo.png"
                                    />
                                    {errors.logo && (
                                        <p className="text-sm text-destructive">{errors.logo}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="credential_url">Credential URL</Label>
                                    <Input
                                        id="credential_url"
                                        type="url"
                                        value={data.credential_url}
                                        onChange={(e) => setData('credential_url', e.target.value)}
                                        placeholder="https://credential-verification-url.com"
                                    />
                                    {errors.credential_url && (
                                        <p className="text-sm text-destructive">{errors.credential_url}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="sort_order">Sort Order</Label>
                                    <Input
                                        id="sort_order"
                                        type="number"
                                        value={data.sort_order}
                                        onChange={(e) => setData('sort_order', parseInt(e.target.value) || 0)}
                                        min="0"
                                    />
                                </div>

                                <div className="flex justify-end gap-2">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => setIsDialogOpen(false)}
                                    >
                                        Cancel
                                    </Button>
                                    <Button type="submit" disabled={processing}>
                                        {processing ? 'Saving...' : editingCertification ? 'Update' : 'Create'}
                                    </Button>
                                </div>
                            </form>
                        </DialogContent>
                    </Dialog>
                </div>

                <div className="grid gap-4">
                    {certifications.map((certification) => (
                        <Card key={certification.id}>
                            <CardHeader>
                                <div className="flex justify-between items-start">
                                    <div className="flex items-start gap-3">
                                        <div className="p-2 bg-yellow-500/10 rounded-lg">
                                            <Award className="h-5 w-5 text-yellow-600" />
                                        </div>
                                        <div>
                                            <CardTitle className="text-lg">{certification.name}</CardTitle>
                                            <CardDescription>
                                                {certification.issuer} • {certification.date}
                                            </CardDescription>
                                        </div>
                                    </div>
                                    <div className="flex gap-2">
                                        {certification.credential_url && (
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                asChild
                                            >
                                                <a href={certification.credential_url} target="_blank" rel="noopener noreferrer">
                                                    <ExternalLink className="h-4 w-4" />
                                                </a>
                                            </Button>
                                        )}
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => openEditDialog(certification)}
                                        >
                                            <Edit className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => deleteCertification(certification)}
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            </CardHeader>
                        </Card>
                    ))}
                </div>

                {certifications.length === 0 && (
                    <Card>
                        <CardContent className="text-center py-8">
                            <p className="text-muted-foreground">
                                No certifications added yet. Click "Add Certification" to get started.
                            </p>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
