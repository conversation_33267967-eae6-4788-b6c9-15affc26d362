import AppLayout from '@/layouts/app-layout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler, useState } from 'react';
import { Plus, Edit, Trash2, ExternalLink } from 'lucide-react';
import { toast } from 'sonner';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';

interface SocialLink {
    id: number;
    platform: string;
    url: string;
    icon?: string;
    sort_order: number;
}

interface Props {
    socialLinks: SocialLink[];
}

export default function SocialLinksPage({ socialLinks }: Props) {
    const [editingLink, setEditingLink] = useState<SocialLink | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    const { data, setData, post, put, delete: destroy, processing, errors, reset } = useForm({
        platform: '',
        url: '',
        icon: '',
        sort_order: 0,
    });

    const openCreateDialog = () => {
        reset();
        setEditingLink(null);
        setIsDialogOpen(true);
    };

    const openEditDialog = (link: SocialLink) => {
        setData({
            platform: link.platform,
            url: link.url,
            icon: link.icon || '',
            sort_order: link.sort_order,
        });
        setEditingLink(link);
        setIsDialogOpen(true);
    };

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        if (editingLink) {
            put(route('portfolio.social-links.update', editingLink.id), {
                onSuccess: () => {
                    toast.success('Social link updated successfully!');
                    setIsDialogOpen(false);
                },
                onError: () => {
                    toast.error('Failed to update social link.');
                },
            });
        } else {
            post(route('portfolio.social-links.store'), {
                onSuccess: () => {
                    toast.success('Social link added successfully!');
                    setIsDialogOpen(false);
                },
                onError: () => {
                    toast.error('Failed to add social link.');
                },
            });
        }
    };

    const deleteLink = (link: SocialLink) => {
        if (confirm('Are you sure you want to delete this social link?')) {
            destroy(route('portfolio.social-links.destroy', link.id), {
                onSuccess: () => {
                    toast.success('Social link deleted successfully!');
                },
                onError: () => {
                    toast.error('Failed to delete social link.');
                },
            });
        }
    };

    return (
        <AppLayout>
            <Head title="Social Links Management"/>
            <div className="space-y-6 p-6">
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-3xl font-bold">Social Links Management</h1>
                        <p className="text-muted-foreground">
                            Manage your social media and professional profile links.
                        </p>
                    </div>
                    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                        <DialogTrigger asChild>
                            <Button onClick={openCreateDialog}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Social Link
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>
                                    {editingLink ? 'Edit Social Link' : 'Add New Social Link'}
                                </DialogTitle>
                                <DialogDescription>
                                    {editingLink
                                        ? 'Update your social media or professional profile link.'
                                        : 'Add a new social media or professional profile link.'
                                    }
                                </DialogDescription>
                            </DialogHeader>

                            <form onSubmit={submit} className="space-y-4">
                                <div>
                                    <Label htmlFor="platform">Platform *</Label>
                                    <Input
                                        id="platform"
                                        value={data.platform}
                                        onChange={(e) => setData('platform', e.target.value)}
                                        placeholder="e.g., GitHub, LinkedIn, Twitter"
                                        required
                                    />
                                    {errors.platform && (
                                        <p className="text-sm text-destructive">{errors.platform}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="url">URL *</Label>
                                    <Input
                                        id="url"
                                        type="url"
                                        value={data.url}
                                        onChange={(e) => setData('url', e.target.value)}
                                        placeholder="https://github.com/username"
                                        required
                                    />
                                    {errors.url && (
                                        <p className="text-sm text-destructive">{errors.url}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="icon">Icon Name</Label>
                                    <Input
                                        id="icon"
                                        value={data.icon}
                                        onChange={(e) => setData('icon', e.target.value)}
                                        placeholder="e.g., Github, Linkedin, Twitter"
                                    />
                                    <p className="text-xs text-muted-foreground mt-1">
                                        Icon name for Lucide React icons (optional)
                                    </p>
                                    {errors.icon && (
                                        <p className="text-sm text-destructive">{errors.icon}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="sort_order">Sort Order</Label>
                                    <Input
                                        id="sort_order"
                                        type="number"
                                        value={data.sort_order}
                                        onChange={(e) => setData('sort_order', parseInt(e.target.value) || 0)}
                                        min="0"
                                    />
                                </div>

                                <div className="flex justify-end gap-2">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => setIsDialogOpen(false)}
                                    >
                                        Cancel
                                    </Button>
                                    <Button type="submit" disabled={processing}>
                                        {processing ? 'Saving...' : editingLink ? 'Update' : 'Create'}
                                    </Button>
                                </div>
                            </form>
                        </DialogContent>
                    </Dialog>
                </div>

                <div className="grid gap-4">
                    {socialLinks.map((link) => (
                        <Card key={link.id}>
                            <CardHeader>
                                <div className="flex justify-between items-center">
                                    <div className="flex items-center gap-3">
                                        <div className="p-2 bg-primary/10 rounded-lg">
                                            <ExternalLink className="h-5 w-5 text-primary" />
                                        </div>
                                        <div>
                                            <CardTitle className="text-lg">{link.platform}</CardTitle>
                                            <CardDescription>
                                                <a
                                                    href={link.url}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-blue-600 hover:underline"
                                                >
                                                    {link.url}
                                                </a>
                                            </CardDescription>
                                        </div>
                                    </div>
                                    <div className="flex gap-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            asChild
                                        >
                                            <a href={link.url} target="_blank" rel="noopener noreferrer">
                                                <ExternalLink className="h-4 w-4" />
                                            </a>
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => openEditDialog(link)}
                                        >
                                            <Edit className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => deleteLink(link)}
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            </CardHeader>
                        </Card>
                    ))}
                </div>

                {socialLinks.length === 0 && (
                    <Card>
                        <CardContent className="text-center py-8">
                            <p className="text-muted-foreground">
                                No social links added yet. Click "Add Social Link" to get started.
                            </p>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
