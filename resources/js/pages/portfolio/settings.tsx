import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler, useState } from 'react';
import { X, Plus } from 'lucide-react';
import { toast } from 'sonner';

interface PortfolioSettings {
    id?: number;
    meta_title?: string;
    meta_description?: string;
    name: string;
    title: string;
    location?: string;
    avatar?: string;
    email: string;
    phone?: string;
    working_hours?: string;
    available_for_work: boolean;
    badges?: string[];
    bio?: string;
    focus?: string[];
    languages?: Array<{
        name: string;
        proficiency: string;
        level: number;
        flag: string;
    }>;
    interests?: string[];
}

interface Props {
    settings?: PortfolioSettings;
}

export default function PortfolioSettingsPage({ settings }: Props) {
    const { data, setData, post, processing, errors } = useForm({
        meta_title: settings?.meta_title || '',
        meta_description: settings?.meta_description || '',
        name: settings?.name || '',
        title: settings?.title || '',
        location: settings?.location || '',
        avatar: settings?.avatar || '',
        email: settings?.email || '',
        phone: settings?.phone || '',
        working_hours: settings?.working_hours || '',
        available_for_work: settings?.available_for_work || true,
        badges: settings?.badges || [],
        bio: settings?.bio || '',
        focus: settings?.focus || [],
        languages: settings?.languages || [],
        interests: settings?.interests || [],
    });

    const [newBadge, setNewBadge] = useState('');
    const [newFocus, setNewFocus] = useState('');
    const [newInterest, setNewInterest] = useState('');

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('portfolio.settings.store'), {
            onSuccess: () => {
                toast.success('Portfolio settings updated successfully!');
            },
            onError: () => {
                toast.error('Failed to update portfolio settings.');
            },
        });
    };

    const addBadge = () => {
        if (newBadge.trim()) {
            setData('badges', [...data.badges, newBadge.trim()]);
            setNewBadge('');
        }
    };

    const removeBadge = (index: number) => {
        setData('badges', data.badges.filter((_, i) => i !== index));
    };

    const addFocus = () => {
        if (newFocus.trim()) {
            setData('focus', [...data.focus, newFocus.trim()]);
            setNewFocus('');
        }
    };

    const removeFocus = (index: number) => {
        setData('focus', data.focus.filter((_, i) => i !== index));
    };

    const addInterest = () => {
        if (newInterest.trim()) {
            setData('interests', [...data.interests, newInterest.trim()]);
            setNewInterest('');
        }
    };

    const removeInterest = (index: number) => {
        setData('interests', data.interests.filter((_, i) => i !== index));
    };

    return (
        <AppLayout>
            <Head title="Portfolio Settings"/>
            <div className="space-y-6 p-6">
                <div>
                    <h1 className="text-3xl font-bold">Portfolio Settings</h1>
                    <p className="text-muted-foreground">
                        Manage your portfolio information and personal details.
                    </p>
                </div>

                <form onSubmit={submit} className="space-y-6">
                    {/* Meta Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Meta Information</CardTitle>
                            <CardDescription>
                                SEO and meta information for your portfolio.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <Label htmlFor="meta_title">Meta Title</Label>
                                <Input
                                    id="meta_title"
                                    value={data.meta_title}
                                    onChange={(e) => setData('meta_title', e.target.value)}
                                    placeholder="Your Portfolio Title"
                                />
                                {errors.meta_title && (
                                    <p className="text-sm text-destructive">{errors.meta_title}</p>
                                )}
                            </div>
                            <div>
                                <Label htmlFor="meta_description">Meta Description</Label>
                                <Textarea
                                    id="meta_description"
                                    value={data.meta_description}
                                    onChange={(e) => setData('meta_description', e.target.value)}
                                    placeholder="Brief description of your portfolio"
                                />
                                {errors.meta_description && (
                                    <p className="text-sm text-destructive">{errors.meta_description}</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Personal Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Personal Information</CardTitle>
                            <CardDescription>
                                Your basic personal and contact information.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="name">Full Name *</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        required
                                    />
                                    {errors.name && (
                                        <p className="text-sm text-destructive">{errors.name}</p>
                                    )}
                                </div>
                                <div>
                                    <Label htmlFor="title">Professional Title *</Label>
                                    <Input
                                        id="title"
                                        value={data.title}
                                        onChange={(e) => setData('title', e.target.value)}
                                        placeholder="e.g., Full-stack Developer"
                                        required
                                    />
                                    {errors.title && (
                                        <p className="text-sm text-destructive">{errors.title}</p>
                                    )}
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="email">Email *</Label>
                                    <Input
                                        id="email"
                                        type="email"
                                        value={data.email}
                                        onChange={(e) => setData('email', e.target.value)}
                                        required
                                    />
                                    {errors.email && (
                                        <p className="text-sm text-destructive">{errors.email}</p>
                                    )}
                                </div>
                                <div>
                                    <Label htmlFor="phone">Phone</Label>
                                    <Input
                                        id="phone"
                                        value={data.phone}
                                        onChange={(e) => setData('phone', e.target.value)}
                                        placeholder="+1234567890"
                                    />
                                    {errors.phone && (
                                        <p className="text-sm text-destructive">{errors.phone}</p>
                                    )}
                                </div>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="location">Location</Label>
                                    <Input
                                        id="location"
                                        value={data.location}
                                        onChange={(e) => setData('location', e.target.value)}
                                        placeholder="City, Country"
                                    />
                                    {errors.location && (
                                        <p className="text-sm text-destructive">{errors.location}</p>
                                    )}
                                </div>
                                <div>
                                    <Label htmlFor="working_hours">Working Hours</Label>
                                    <Input
                                        id="working_hours"
                                        value={data.working_hours}
                                        onChange={(e) => setData('working_hours', e.target.value)}
                                        placeholder="Monday - Friday, 9am - 5pm"
                                    />
                                    {errors.working_hours && (
                                        <p className="text-sm text-destructive">{errors.working_hours}</p>
                                    )}
                                </div>
                            </div>

                            <div>
                                <Label htmlFor="avatar">Avatar URL</Label>
                                <Input
                                    id="avatar"
                                    value={data.avatar}
                                    onChange={(e) => setData('avatar', e.target.value)}
                                    placeholder="https://example.com/avatar.jpg"
                                />
                                {errors.avatar && (
                                    <p className="text-sm text-destructive">{errors.avatar}</p>
                                )}
                            </div>

                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="available_for_work"
                                    checked={data.available_for_work}
                                    onCheckedChange={(checked) => setData('available_for_work', checked)}
                                />
                                <Label htmlFor="available_for_work">Available for work</Label>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Professional Badges */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Professional Badges</CardTitle>
                            <CardDescription>
                                Add badges that represent your key skills or specializations.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex gap-2">
                                <Input
                                    value={newBadge}
                                    onChange={(e) => setNewBadge(e.target.value)}
                                    placeholder="Add a badge"
                                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addBadge())}
                                />
                                <Button type="button" onClick={addBadge} size="sm">
                                    <Plus className="h-4 w-4" />
                                </Button>
                            </div>
                            <div className="flex flex-wrap gap-2">
                                {data.badges.map((badge, index) => (
                                    <Badge key={index} variant="secondary" className="flex items-center gap-1">
                                        {badge}
                                        <button
                                            type="button"
                                            onClick={() => removeBadge(index)}
                                            className="ml-1 hover:text-destructive"
                                        >
                                            <X className="h-3 w-3" />
                                        </button>
                                    </Badge>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    <div className="flex justify-end">
                        <Button type="submit" disabled={processing}>
                            {processing ? 'Saving...' : 'Save Settings'}
                        </Button>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
