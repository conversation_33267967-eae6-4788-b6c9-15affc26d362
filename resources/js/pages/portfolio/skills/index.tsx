import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler, useState } from 'react';
import { Plus, Edit, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';

interface Skill {
    id: number;
    name: string;
    category: 'design' | 'development' | 'ux_methods' | 'soft_skills';
    proficiency: number;
    sort_order: number;
}

interface Props {
    skills: Skill[];
}

const categoryLabels = {
    design: 'Design',
    development: 'Development',
    ux_methods: 'UX Methods',
    soft_skills: 'Soft Skills',
};

export default function SkillsPage({ skills }: Props) {
    const [editingSkill, setEditingSkill] = useState<Skill | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    const { data, setData, post, put, delete: destroy, processing, errors, reset } = useForm({
        name: '',
        category: 'development' as Skill['category'],
        proficiency: 50,
        sort_order: 0,
    });

    const openCreateDialog = () => {
        reset();
        setEditingSkill(null);
        setIsDialogOpen(true);
    };

    const openEditDialog = (skill: Skill) => {
        setData({
            name: skill.name,
            category: skill.category,
            proficiency: skill.proficiency,
            sort_order: skill.sort_order,
        });
        setEditingSkill(skill);
        setIsDialogOpen(true);
    };

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        if (editingSkill) {
            put(route('portfolio.skills.update', editingSkill.id), {
                onSuccess: () => {
                    toast.success('Skill updated successfully!');
                    setIsDialogOpen(false);
                },
                onError: () => {
                    toast.error('Failed to update skill.');
                },
            });
        } else {
            post(route('portfolio.skills.store'), {
                onSuccess: () => {
                    toast.success('Skill added successfully!');
                    setIsDialogOpen(false);
                },
                onError: () => {
                    toast.error('Failed to add skill.');
                },
            });
        }
    };

    const deleteSkill = (skill: Skill) => {
        if (confirm('Are you sure you want to delete this skill?')) {
            destroy(route('portfolio.skills.destroy', skill.id), {
                onSuccess: () => {
                    toast.success('Skill deleted successfully!');
                },
                onError: () => {
                    toast.error('Failed to delete skill.');
                },
            });
        }
    };

    // Group skills by category
    const groupedSkills = skills.reduce((acc, skill) => {
        if (!acc[skill.category]) {
            acc[skill.category] = [];
        }
        acc[skill.category].push(skill);
        return acc;
    }, {} as Record<string, Skill[]>);

    return (
        <AppLayout >
            <Head title="Skills Management"/>
            <div className="space-y-6 p-6">
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-3xl font-bold">Skills Management</h1>
                        <p className="text-muted-foreground">
                            Manage your technical and soft skills with proficiency levels.
                        </p>
                    </div>
                    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                        <DialogTrigger asChild>
                            <Button onClick={openCreateDialog}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Skill
                            </Button>
                        </DialogTrigger>
                        <DialogContent>
                            <DialogHeader>
                                <DialogTitle>
                                    {editingSkill ? 'Edit Skill' : 'Add New Skill'}
                                </DialogTitle>
                                <DialogDescription>
                                    {editingSkill
                                        ? 'Update your skill details and proficiency level.'
                                        : 'Add a new skill to your portfolio with proficiency level.'
                                    }
                                </DialogDescription>
                            </DialogHeader>

                            <form onSubmit={submit} className="space-y-4">
                                <div>
                                    <Label htmlFor="name">Skill Name *</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder="e.g., React, JavaScript, Leadership"
                                        required
                                    />
                                    {errors.name && (
                                        <p className="text-sm text-destructive">{errors.name}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="category">Category *</Label>
                                    <Select value={data.category} onValueChange={(value) => setData('category', value as Skill['category'])}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select a category" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="development">Development</SelectItem>
                                            <SelectItem value="design">Design</SelectItem>
                                            <SelectItem value="ux_methods">UX Methods</SelectItem>
                                            <SelectItem value="soft_skills">Soft Skills</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.category && (
                                        <p className="text-sm text-destructive">{errors.category}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="proficiency">Proficiency Level: {data.proficiency}%</Label>
                                    <Input
                                        id="proficiency"
                                        type="range"
                                        min="0"
                                        max="100"
                                        value={data.proficiency}
                                        onChange={(e) => setData('proficiency', parseInt(e.target.value))}
                                        className="mt-2"
                                    />
                                    <Progress value={data.proficiency} className="mt-2" />
                                    {errors.proficiency && (
                                        <p className="text-sm text-destructive">{errors.proficiency}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="sort_order">Sort Order</Label>
                                    <Input
                                        id="sort_order"
                                        type="number"
                                        value={data.sort_order}
                                        onChange={(e) => setData('sort_order', parseInt(e.target.value) || 0)}
                                        min="0"
                                    />
                                </div>

                                <div className="flex justify-end gap-2">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => setIsDialogOpen(false)}
                                    >
                                        Cancel
                                    </Button>
                                    <Button type="submit" disabled={processing}>
                                        {processing ? 'Saving...' : editingSkill ? 'Update' : 'Create'}
                                    </Button>
                                </div>
                            </form>
                        </DialogContent>
                    </Dialog>
                </div>

                <div className="grid gap-6">
                    {Object.entries(groupedSkills).map(([category, categorySkills]) => (
                        <Card key={category}>
                            <CardHeader>
                                <CardTitle>{categoryLabels[category as keyof typeof categoryLabels]}</CardTitle>
                                <CardDescription>
                                    {categorySkills.length} skill{categorySkills.length !== 1 ? 's' : ''}
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="grid gap-4">
                                    {categorySkills.map((skill) => (
                                        <div key={skill.id} className="flex items-center justify-between p-3 border rounded-lg">
                                            <div className="flex-1">
                                                <div className="flex items-center justify-between mb-2">
                                                    <h4 className="font-medium">{skill.name}</h4>
                                                    <span className="text-sm text-muted-foreground">
                                                        {skill.proficiency}%
                                                    </span>
                                                </div>
                                                <Progress value={skill.proficiency} className="h-2" />
                                            </div>
                                            <div className="flex gap-2 ml-4">
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => openEditDialog(skill)}
                                                >
                                                    <Edit className="h-4 w-4" />
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => deleteSkill(skill)}
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>

                {skills.length === 0 && (
                    <Card>
                        <CardContent className="text-center py-8">
                            <p className="text-muted-foreground">
                                No skills added yet. Click "Add Skill" to get started.
                            </p>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
