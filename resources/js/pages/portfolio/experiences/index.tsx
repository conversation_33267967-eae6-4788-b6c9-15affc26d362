import AppLayout from '@/layouts/app-layout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Head, useForm } from '@inertiajs/react';
import { FormEventHandler, useState } from 'react';
import { X, Plus, Edit, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/ui/dialog';

interface Experience {
    id: number;
    title: string;
    company: string;
    period: string;
    description?: string;
    achievements?: string[];
    technologies?: string[];
    sort_order: number;
}

interface Props {
    experiences: Experience[];
}

export default function ExperiencesPage({ experiences }: Props) {
    const [editingExperience, setEditingExperience] = useState<Experience | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);

    const { data, setData, post, put, delete: destroy, processing, errors, reset } = useForm({
        title: '',
        company: '',
        period: '',
        description: '',
        achievements: [] as string[],
        technologies: [] as string[],
        sort_order: 0,
    });

    const [newTechnology, setNewTechnology] = useState('');
    const [newAchievement, setNewAchievement] = useState('');

    const openCreateDialog = () => {
        reset();
        setEditingExperience(null);
        setIsDialogOpen(true);
    };

    const openEditDialog = (experience: Experience) => {
        setData({
            title: experience.title,
            company: experience.company,
            period: experience.period,
            description: experience.description || '',
            achievements: experience.achievements || [],
            technologies: experience.technologies || [],
            sort_order: experience.sort_order,
        });
        setEditingExperience(experience);
        setIsDialogOpen(true);
    };

    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        if (editingExperience) {
            put(route('portfolio.experiences.update', editingExperience.id), {
                onSuccess: () => {
                    toast.success('Experience updated successfully!');
                    setIsDialogOpen(false);
                },
                onError: () => {
                    toast.error('Failed to update experience.');
                },
            });
        } else {
            post(route('portfolio.experiences.store'), {
                onSuccess: () => {
                    toast.success('Experience added successfully!');
                    setIsDialogOpen(false);
                },
                onError: () => {
                    toast.error('Failed to add experience.');
                },
            });
        }
    };

    const deleteExperience = (experience: Experience) => {
        if (confirm('Are you sure you want to delete this experience?')) {
            destroy(route('portfolio.experiences.destroy', experience.id), {
                onSuccess: () => {
                    toast.success('Experience deleted successfully!');
                },
                onError: () => {
                    toast.error('Failed to delete experience.');
                },
            });
        }
    };

    const addTechnology = () => {
        if (newTechnology.trim()) {
            setData('technologies', [...data.technologies, newTechnology.trim()]);
            setNewTechnology('');
        }
    };

    const removeTechnology = (index: number) => {
        setData('technologies', data.technologies.filter((_, i) => i !== index));
    };

    const addAchievement = () => {
        if (newAchievement.trim()) {
            setData('achievements', [...data.achievements, newAchievement.trim()]);
            setNewAchievement('');
        }
    };

    const removeAchievement = (index: number) => {
        setData('achievements', data.achievements.filter((_, i) => i !== index));
    };

    return (
        <AppLayout>
            <Head title="Experience Management"/>
            <div className="space-y-6 p-6">
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-3xl font-bold">Experience Management</h1>
                        <p className="text-muted-foreground">
                            Manage your work experience and professional history.
                        </p>
                    </div>
                    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                        <DialogTrigger asChild>
                            <Button onClick={openCreateDialog}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Experience
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                            <DialogHeader>
                                <DialogTitle>
                                    {editingExperience ? 'Edit Experience' : 'Add New Experience'}
                                </DialogTitle>
                                <DialogDescription>
                                    {editingExperience
                                        ? 'Update your work experience details.'
                                        : 'Add a new work experience to your portfolio.'
                                    }
                                </DialogDescription>
                            </DialogHeader>

                            <form onSubmit={submit} className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="title">Job Title *</Label>
                                        <Input
                                            id="title"
                                            value={data.title}
                                            onChange={(e) => setData('title', e.target.value)}
                                            required
                                        />
                                        {errors.title && (
                                            <p className="text-sm text-destructive">{errors.title}</p>
                                        )}
                                    </div>
                                    <div>
                                        <Label htmlFor="company">Company *</Label>
                                        <Input
                                            id="company"
                                            value={data.company}
                                            onChange={(e) => setData('company', e.target.value)}
                                            required
                                        />
                                        {errors.company && (
                                            <p className="text-sm text-destructive">{errors.company}</p>
                                        )}
                                    </div>
                                </div>

                                <div>
                                    <Label htmlFor="period">Period *</Label>
                                    <Input
                                        id="period"
                                        value={data.period}
                                        onChange={(e) => setData('period', e.target.value)}
                                        placeholder="e.g., Jan 2020 - Dec 2022"
                                        required
                                    />
                                    {errors.period && (
                                        <p className="text-sm text-destructive">{errors.period}</p>
                                    )}
                                </div>

                                <div>
                                    <Label htmlFor="description">Description</Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        placeholder="Brief description of your role and responsibilities"
                                    />
                                    {errors.description && (
                                        <p className="text-sm text-destructive">{errors.description}</p>
                                    )}
                                </div>

                                {/* Technologies */}
                                <div>
                                    <Label>Technologies Used</Label>
                                    <div className="flex gap-2 mt-2">
                                        <Input
                                            value={newTechnology}
                                            onChange={(e) => setNewTechnology(e.target.value)}
                                            placeholder="Add a technology"
                                            onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTechnology())}
                                        />
                                        <Button type="button" onClick={addTechnology} size="sm">
                                            <Plus className="h-4 w-4" />
                                        </Button>
                                    </div>
                                    <div className="flex flex-wrap gap-2 mt-2">
                                        {data.technologies.map((tech, index) => (
                                            <Badge key={index} variant="secondary" className="flex items-center gap-1">
                                                {tech}
                                                <button
                                                    type="button"
                                                    onClick={() => removeTechnology(index)}
                                                    className="ml-1 hover:text-destructive"
                                                >
                                                    <X className="h-3 w-3" />
                                                </button>
                                            </Badge>
                                        ))}
                                    </div>
                                </div>

                                <div>
                                    <Label htmlFor="sort_order">Sort Order</Label>
                                    <Input
                                        id="sort_order"
                                        type="number"
                                        value={data.sort_order}
                                        onChange={(e) => setData('sort_order', parseInt(e.target.value) || 0)}
                                        min="0"
                                    />
                                </div>

                                <div className="flex justify-end gap-2">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => setIsDialogOpen(false)}
                                    >
                                        Cancel
                                    </Button>
                                    <Button type="submit" disabled={processing}>
                                        {processing ? 'Saving...' : editingExperience ? 'Update' : 'Create'}
                                    </Button>
                                </div>
                            </form>
                        </DialogContent>
                    </Dialog>
                </div>

                <div className="grid gap-4">
                    {experiences.map((experience) => (
                        <Card key={experience.id}>
                            <CardHeader>
                                <div className="flex justify-between items-start">
                                    <div>
                                        <CardTitle>{experience.title}</CardTitle>
                                        <CardDescription>
                                            {experience.company} • {experience.period}
                                        </CardDescription>
                                    </div>
                                    <div className="flex gap-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => openEditDialog(experience)}
                                        >
                                            <Edit className="h-4 w-4" />
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => deleteExperience(experience)}
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            </CardHeader>
                            {(experience.description || experience.technologies?.length) && (
                                <CardContent>
                                    {experience.description && (
                                        <p className="text-sm text-muted-foreground mb-3">
                                            {experience.description}
                                        </p>
                                    )}
                                    {experience.technologies && experience.technologies.length > 0 && (
                                        <div className="flex flex-wrap gap-1">
                                            {experience.technologies.map((tech, index) => (
                                                <Badge key={index} variant="secondary" className="text-xs">
                                                    {tech}
                                                </Badge>
                                            ))}
                                        </div>
                                    )}
                                </CardContent>
                            )}
                        </Card>
                    ))}
                </div>

                {experiences.length === 0 && (
                    <Card>
                        <CardContent className="text-center py-8">
                            <p className="text-muted-foreground">
                                No experiences added yet. Click "Add Experience" to get started.
                            </p>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
