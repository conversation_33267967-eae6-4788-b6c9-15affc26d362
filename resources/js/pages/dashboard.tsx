import AppLayout from '@/layouts/app-layout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { type BreadcrumbItem } from '@/types';
import { Link } from '@inertiajs/react';
import { Settings, Briefcase, Award, FolderOpen, GraduationCap, ExternalLink, User, BarChart3 } from 'lucide-react';
import { Head } from '@inertiajs/react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/',
    },
];

export default function Dashboard() {
    const portfolioSections = [
        {
            title: 'Portfolio Settings',
            description: 'Manage your personal information, bio, and contact details',
            href: '/portfolio/settings',
            icon: Settings,
            color: 'bg-blue-500',
        },
        {
            title: 'Experience',
            description: 'Add and manage your work experience and career history',
            href: '/portfolio/experiences',
            icon: Briefcase,
            color: 'bg-green-500',
        },
        {
            title: 'Skills',
            description: 'Showcase your technical and soft skills with proficiency levels',
            href: '/portfolio/skills',
            icon: Award,
            color: 'bg-purple-500',
        },
        {
            title: 'Projects',
            description: 'Display your portfolio projects and achievements',
            href: '/portfolio/projects',
            icon: FolderOpen,
            color: 'bg-orange-500',
        },
        {
            title: 'Education',
            description: 'Add your educational background and qualifications',
            href: '/portfolio/education',
            icon: GraduationCap,
            color: 'bg-indigo-500',
        },
        {
            title: 'Certifications',
            description: 'Manage your professional certifications and credentials',
            href: '/portfolio/certifications',
            icon: Award,
            color: 'bg-yellow-500',
        },
        {
            title: 'Social Links',
            description: 'Add links to your social media and professional profiles',
            href: '/portfolio/social-links',
            icon: ExternalLink,
            color: 'bg-pink-500',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="space-y-6 p-6">
                <div>
                    <h1 className="text-3xl font-bold">Portfolio Dashboard</h1>
                    <p className="text-muted-foreground">
                        Manage your portfolio content and settings from here.
                    </p>
                </div>

                {/* Quick Stats */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">API Endpoint</CardTitle>
                            <BarChart3 className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">JSON</div>
                            <p className="text-xs text-muted-foreground">
                                <a
                                    href="/api/portfolio"
                                    target="_blank"
                                    className="text-blue-600 hover:underline"
                                >
                                    /api/portfolio
                                </a>
                            </p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Portfolio Status</CardTitle>
                            <User className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">Active</div>
                            <p className="text-xs text-muted-foreground">
                                Portfolio is live and accessible
                            </p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Data Format</CardTitle>
                            <Settings className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">Dynamic</div>
                            <p className="text-xs text-muted-foreground">
                                Real-time updates from database
                            </p>
                        </CardContent>
                    </Card>
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">CRUD Operations</CardTitle>
                            <Award className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">Enabled</div>
                            <p className="text-xs text-muted-foreground">
                                Full create, read, update, delete
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Portfolio Management Sections */}
                <div>
                    <h2 className="text-2xl font-bold mb-4">Portfolio Management</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {portfolioSections.map((section) => (
                            <Card key={section.href} className="hover:shadow-md transition-shadow">
                                <CardHeader>
                                    <div className="flex items-center space-x-3">
                                        <div className={`p-2 rounded-lg ${section.color}`}>
                                            <section.icon className="h-5 w-5 text-white" />
                                        </div>
                                        <div>
                                            <CardTitle className="text-lg">{section.title}</CardTitle>
                                        </div>
                                    </div>
                                    <CardDescription>{section.description}</CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <Button asChild className="w-full">
                                        <Link href={section.href}>
                                            Manage {section.title}
                                        </Link>
                                    </Button>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </div>

                {/* Quick Actions */}
                <Card>
                    <CardHeader>
                        <CardTitle>Quick Actions</CardTitle>
                        <CardDescription>
                            Common tasks and useful links for managing your portfolio.
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-wrap gap-2">
                            <Badge variant="outline" asChild>
                                <a href="/api/portfolio" target="_blank" className="cursor-pointer">
                                    View JSON API
                                </a>
                            </Badge>
                            <Badge variant="outline" asChild>
                                <Link href="/portfolio/settings" className="cursor-pointer">
                                    Update Profile
                                </Link>
                            </Badge>
                            <Badge variant="outline" asChild>
                                <Link href="/portfolio/experiences" className="cursor-pointer">
                                    Add Experience
                                </Link>
                            </Badge>
                            <Badge variant="outline" asChild>
                                <Link href="/portfolio/skills" className="cursor-pointer">
                                    Manage Skills
                                </Link>
                            </Badge>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
