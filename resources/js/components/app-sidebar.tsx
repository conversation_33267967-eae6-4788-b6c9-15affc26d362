import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/react';
import { Globe, LayoutGrid, Settings, Briefcase, Award, GraduationCap, ExternalLink, FolderOpen } from 'lucide-react';
import AppLogo from './app-logo';

const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/',
        icon: LayoutGrid,
    },
];

const portfolioNavItems: NavItem[] = [
    {
        title: 'Portfolio Settings',
        href: '/portfolio/settings',
        icon: Settings,
    },
    {
        title: 'Experience',
        href: '/portfolio/experiences',
        icon: Briefcase,
    },
    {
        title: 'Skills',
        href: '/portfolio/skills',
        icon: Award,
    },
    {
        title: 'Projects',
        href: '/portfolio/projects',
        icon: FolderOpen,
    },
    {
        title: 'Education',
        href: '/portfolio/education',
        icon: GraduationCap,
    },
    {
        title: 'Certifications',
        href: '/portfolio/certifications',
        icon: Award,
    },
    {
        title: 'Social Links',
        href: '/portfolio/social-links',
        icon: ExternalLink,
    },
];

const footerNavItems: NavItem[] = [
    {
        title: 'Website',
        href: 'https://ariful.dev',
        icon: Globe,
    }
];

export function AppSidebar() {
    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={mainNavItems} />
                <NavMain items={portfolioNavItems} title="Portfolio Management" />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
