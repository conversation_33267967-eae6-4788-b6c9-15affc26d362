<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PortfolioSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'meta_title',
        'meta_description',
        'name',
        'title',
        'location',
        'avatar',
        'email',
        'phone',
        'working_hours',
        'available_for_work',
        'badges',
        'bio',
        'focus',
        'languages',
        'interests',
    ];

    protected $casts = [
        'available_for_work' => 'boolean',
        'badges' => 'array',
        'focus' => 'array',
        'languages' => 'array',
        'interests' => 'array',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
