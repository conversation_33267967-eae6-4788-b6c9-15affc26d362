<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PortfolioApiController extends Controller
{
    /**
     * Get portfolio data in JSON format
     */
    public function getPortfolioData(Request $request): JsonResponse
    {
        // Get the user (for now, we'll use the first user, but you can modify this)
        $user = User::with([
            'portfolioSetting',
            'experiences',
            'skills',
            'projects',
            'socialLinks',
            'certifications',
            'education'
        ])->first();

        if (!$user || !$user->portfolioSetting) {
            return response()->json(['error' => 'Portfolio data not found'], 404);
        }

        $settings = $user->portfolioSetting;

        // Build the portfolio data structure
        $portfolioData = [
            'meta' => [
                'title' => $settings->meta_title,
                'description' => $settings->meta_description,
            ],
            'personal' => [
                'name' => $settings->name,
                'title' => $settings->title,
                'location' => $settings->location,
                'avatar' => $settings->avatar,
                'email' => $settings->email,
                'phone' => $settings->phone,
                'workingHours' => $settings->working_hours,
                'availableForWork' => $settings->available_for_work,
                'badges' => $settings->badges ?? [],
                'social' => $user->socialLinks->map(function ($link) {
                    return [
                        'platform' => $link->platform,
                        'url' => $link->url,
                        'icon' => $link->icon,
                    ];
                })->toArray(),
            ],
            'about' => [
                'bio' => $settings->bio,
                'focus' => $settings->focus ?? [],
                'languages' => $settings->languages ?? [],
                'interests' => $settings->interests ?? [],
            ],
            'navigation' => [
                ['label' => 'Home', 'href' => '/'],
                ['label' => 'Experience', 'href' => '/#experience'],
                ['label' => 'Skills', 'href' => '/#skills'],
                ['label' => 'Projects', 'href' => '/projects'],
                ['label' => 'Contact', 'href' => '/contact'],
            ],
            'experience' => $user->experiences->map(function ($exp) {
                return [
                    'title' => $exp->title,
                    'company' => $exp->company,
                    'period' => $exp->period,
                    'description' => $exp->description,
                    'achievements' => $exp->achievements ?? [],
                    'technologies' => $exp->technologies ?? [],
                ];
            })->toArray(),
            'credentials' => [
                'certifications' => $user->certifications->map(function ($cert) {
                    return [
                        'name' => $cert->name,
                        'issuer' => $cert->issuer,
                        'date' => $cert->date,
                        'logo' => $cert->logo,
                    ];
                })->toArray(),
                'education' => $user->education->map(function ($edu) {
                    return [
                        'degree' => $edu->degree,
                        'institution' => $edu->institution,
                        'year' => $edu->year,
                        'logo' => $edu->logo,
                    ];
                })->toArray(),
                'skills' => $user->skills->where('category', 'development')->pluck('name')->toArray(),
            ],
            'technicalSkills' => [
                'design' => $user->skills->where('category', 'design')->pluck('name')->toArray(),
                'development' => $user->skills->where('category', 'development')->pluck('name')->toArray(),
                'uxMethods' => $user->skills->where('category', 'ux_methods')->pluck('name')->toArray(),
                'softSkills' => $user->skills->where('category', 'soft_skills')->pluck('name')->toArray(),
            ],
        ];

        return response()->json($portfolioData);
    }

    /**
     * Get portfolio data for a specific user
     */
    public function getUserPortfolioData(User $user): JsonResponse
    {
        $user->load([
            'portfolioSetting',
            'experiences',
            'skills',
            'projects',
            'socialLinks',
            'certifications',
            'education'
        ]);

        if (!$user->portfolioSetting) {
            return response()->json(['error' => 'Portfolio data not found for this user'], 404);
        }

        $settings = $user->portfolioSetting;

        // Build the portfolio data structure (same as above)
        $portfolioData = [
            'meta' => [
                'title' => $settings->meta_title,
                'description' => $settings->meta_description,
            ],
            'personal' => [
                'name' => $settings->name,
                'title' => $settings->title,
                'location' => $settings->location,
                'avatar' => $settings->avatar,
                'email' => $settings->email,
                'phone' => $settings->phone,
                'workingHours' => $settings->working_hours,
                'availableForWork' => $settings->available_for_work,
                'badges' => $settings->badges ?? [],
                'social' => $user->socialLinks->map(function ($link) {
                    return [
                        'platform' => $link->platform,
                        'url' => $link->url,
                        'icon' => $link->icon,
                    ];
                })->toArray(),
            ],
            'about' => [
                'bio' => $settings->bio,
                'focus' => $settings->focus ?? [],
                'languages' => $settings->languages ?? [],
                'interests' => $settings->interests ?? [],
            ],
            'navigation' => [
                ['label' => 'Home', 'href' => '/'],
                ['label' => 'Experience', 'href' => '/#experience'],
                ['label' => 'Skills', 'href' => '/#skills'],
                ['label' => 'Projects', 'href' => '/projects'],
                ['label' => 'Contact', 'href' => '/contact'],
            ],
            'experience' => $user->experiences->map(function ($exp) {
                return [
                    'title' => $exp->title,
                    'company' => $exp->company,
                    'period' => $exp->period,
                    'description' => $exp->description,
                    'achievements' => $exp->achievements ?? [],
                    'technologies' => $exp->technologies ?? [],
                ];
            })->toArray(),
            'credentials' => [
                'certifications' => $user->certifications->map(function ($cert) {
                    return [
                        'name' => $cert->name,
                        'issuer' => $cert->issuer,
                        'date' => $cert->date,
                        'logo' => $cert->logo,
                    ];
                })->toArray(),
                'education' => $user->education->map(function ($edu) {
                    return [
                        'degree' => $edu->degree,
                        'institution' => $edu->institution,
                        'year' => $edu->year,
                        'logo' => $edu->logo,
                    ];
                })->toArray(),
                'skills' => $user->skills->where('category', 'development')->pluck('name')->toArray(),
            ],
            'technicalSkills' => [
                //'design' => $user->skills->where('category', 'design')->pluck('name')->toArray(),
                'development' => $user->skills->where('category', 'development')->pluck('name')->toArray(),
                //'uxMethods' => $user->skills->where('category', 'ux_methods')->pluck('name')->toArray(),
                'softSkills' => $user->skills->where('category', 'soft_skills')->pluck('name')->toArray(),
            ],
        ];

        return response()->json($portfolioData);
    }
}
