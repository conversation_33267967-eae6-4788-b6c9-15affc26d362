<?php

namespace App\Http\Controllers\Portfolio;

use App\Http\Controllers\Controller;
use App\Models\Experience;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ExperienceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): Response
    {
        $experiences = auth()->user()->experiences;

        return Inertia::render('portfolio/experiences/index', [
            'experiences' => $experiences,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'company' => 'required|string|max:255',
            'period' => 'required|string|max:255',
            'description' => 'nullable|string',
            'achievements' => 'nullable|array',
            'technologies' => 'nullable|array',
            'sort_order' => 'integer|min:0',
        ]);

        $validated['user_id'] = auth()->id();

        Experience::create($validated);

        return back()->with('success', 'Experience added successfully!');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Experience $experience)
    {
        // Ensure the experience belongs to the authenticated user
        if ($experience->user_id !== auth()->id()) {
            abort(403);
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'company' => 'required|string|max:255',
            'period' => 'required|string|max:255',
            'description' => 'nullable|string',
            'achievements' => 'nullable|array',
            'technologies' => 'nullable|array',
            'sort_order' => 'integer|min:0',
        ]);

        $experience->update($validated);

        return back()->with('success', 'Experience updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Experience $experience)
    {
        // Ensure the experience belongs to the authenticated user
        if ($experience->user_id !== auth()->id()) {
            abort(403);
        }

        $experience->delete();

        return back()->with('success', 'Experience deleted successfully!');
    }
}
