<?php

namespace App\Http\Controllers\Portfolio;

use App\Http\Controllers\Controller;
use App\Models\Certification;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class CertificationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): Response
    {
        $certifications = auth()->user()->certifications;

        return Inertia::render('portfolio/certifications/index', [
            'certifications' => $certifications,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'issuer' => 'required|string|max:255',
            'date' => 'required|string|max:255',
            'logo' => 'nullable|string|max:500',
            'credential_url' => 'nullable|url|max:500',
            'sort_order' => 'integer|min:0',
        ]);

        $validated['user_id'] = auth()->id();

        Certification::create($validated);

        return back()->with('success', 'Certification added successfully!');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Certification $certification)
    {
        // Ensure the certification belongs to the authenticated user
        if ($certification->user_id !== auth()->id()) {
            abort(403);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'issuer' => 'required|string|max:255',
            'date' => 'required|string|max:255',
            'logo' => 'nullable|string|max:500',
            'credential_url' => 'nullable|url|max:500',
            'sort_order' => 'integer|min:0',
        ]);

        $certification->update($validated);

        return back()->with('success', 'Certification updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Certification $certification)
    {
        // Ensure the certification belongs to the authenticated user
        if ($certification->user_id !== auth()->id()) {
            abort(403);
        }

        $certification->delete();

        return back()->with('success', 'Certification deleted successfully!');
    }
}
