<?php

namespace App\Http\Controllers\Portfolio;

use App\Http\Controllers\Controller;
use App\Models\SocialLink;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class SocialLinkController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): Response
    {
        $socialLinks = auth()->user()->socialLinks;

        return Inertia::render('portfolio/social-links/index', [
            'socialLinks' => $socialLinks,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'platform' => 'required|string|max:255',
            'url' => 'required|url|max:500',
            'icon' => 'nullable|string|max:255',
            'sort_order' => 'integer|min:0',
        ]);

        $validated['user_id'] = auth()->id();

        SocialLink::create($validated);

        return back()->with('success', 'Social link added successfully!');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SocialLink $socialLink)
    {
        // Ensure the social link belongs to the authenticated user
        if ($socialLink->user_id !== auth()->id()) {
            abort(403);
        }

        $validated = $request->validate([
            'platform' => 'required|string|max:255',
            'url' => 'required|url|max:500',
            'icon' => 'nullable|string|max:255',
            'sort_order' => 'integer|min:0',
        ]);

        $socialLink->update($validated);

        return back()->with('success', 'Social link updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SocialLink $socialLink)
    {
        // Ensure the social link belongs to the authenticated user
        if ($socialLink->user_id !== auth()->id()) {
            abort(403);
        }

        $socialLink->delete();

        return back()->with('success', 'Social link deleted successfully!');
    }
}
