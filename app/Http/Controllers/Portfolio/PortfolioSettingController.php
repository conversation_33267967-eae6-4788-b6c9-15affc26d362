<?php

namespace App\Http\Controllers\Portfolio;

use App\Http\Controllers\Controller;
use App\Models\PortfolioSetting;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class PortfolioSettingController extends Controller
{
    /**
     * Display the portfolio settings form.
     */
    public function index(): Response
    {
        $settings = auth()->user()->portfolioSetting;

        return Inertia::render('portfolio/settings', [
            'settings' => $settings,
        ]);
    }

    /**
     * Store or update portfolio settings.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'name' => 'required|string|max:255',
            'title' => 'required|string|max:255',
            'location' => 'nullable|string|max:255',
            'avatar' => 'nullable|string|max:500',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:50',
            'working_hours' => 'nullable|string|max:255',
            'available_for_work' => 'boolean',
            'badges' => 'nullable|array',
            'bio' => 'nullable|string|max:1000',
            'focus' => 'nullable|array',
            'languages' => 'nullable|array',
            'interests' => 'nullable|array',
        ]);

        $validated['user_id'] = auth()->id();

        PortfolioSetting::updateOrCreate(
            ['user_id' => auth()->id()],
            $validated
        );

        return back()->with('success', 'Portfolio settings updated successfully!');
    }
}
