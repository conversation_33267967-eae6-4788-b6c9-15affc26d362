<?php

namespace App\Http\Controllers\Portfolio;

use App\Http\Controllers\Controller;
use App\Models\Education;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class EducationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): Response
    {
        $education = auth()->user()->education;

        return Inertia::render('portfolio/education/index', [
            'education' => $education,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'degree' => 'required|string|max:255',
            'institution' => 'required|string|max:255',
            'year' => 'required|string|max:255',
            'logo' => 'nullable|string|max:500',
            'description' => 'nullable|string',
            'sort_order' => 'integer|min:0',
        ]);

        $validated['user_id'] = auth()->id();

        Education::create($validated);

        return back()->with('success', 'Education added successfully!');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Education $education)
    {
        // Ensure the education belongs to the authenticated user
        if ($education->user_id !== auth()->id()) {
            abort(403);
        }

        $validated = $request->validate([
            'degree' => 'required|string|max:255',
            'institution' => 'required|string|max:255',
            'year' => 'required|string|max:255',
            'logo' => 'nullable|string|max:500',
            'description' => 'nullable|string',
            'sort_order' => 'integer|min:0',
        ]);

        $education->update($validated);

        return back()->with('success', 'Education updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Education $education)
    {
        // Ensure the education belongs to the authenticated user
        if ($education->user_id !== auth()->id()) {
            abort(403);
        }

        $education->delete();

        return back()->with('success', 'Education deleted successfully!');
    }
}
