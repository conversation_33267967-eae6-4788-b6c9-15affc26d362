<?php

namespace App\Http\Controllers\Portfolio;

use App\Http\Controllers\Controller;
use App\Models\Skill;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class SkillController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): Response
    {
        $skills = auth()->user()->skills()->orderBy('category')->orderBy('sort_order')->get();

        return Inertia::render('portfolio/skills/index', [
            'skills' => $skills,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'category' => 'required|in:design,development,ux_methods,soft_skills',
            'proficiency' => 'required|integer|min:0|max:100',
            'sort_order' => 'integer|min:0',
        ]);

        $validated['user_id'] = auth()->id();

        Skill::create($validated);

        return back()->with('success', 'Skill added successfully!');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Skill $skill)
    {
        // Ensure the skill belongs to the authenticated user
        if ($skill->user_id !== auth()->id()) {
            abort(403);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'category' => 'required|in:design,development,ux_methods,soft_skills',
            'proficiency' => 'required|integer|min:0|max:100',
            'sort_order' => 'integer|min:0',
        ]);

        $skill->update($validated);

        return back()->with('success', 'Skill updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Skill $skill)
    {
        // Ensure the skill belongs to the authenticated user
        if ($skill->user_id !== auth()->id()) {
            abort(403);
        }

        $skill->delete();

        return back()->with('success', 'Skill deleted successfully!');
    }
}
