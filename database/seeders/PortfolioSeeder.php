<?php

namespace Database\Seeders;

use App\Models\Certification;
use App\Models\Education;
use App\Models\Experience;
use App\Models\PortfolioSetting;
use App\Models\Project;
use App\Models\Skill;
use App\Models\SocialLink;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PortfolioSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user (admin user)
        $user = User::first();

        if (!$user) {
            $this->command->error('No user found. Please run the DatabaseSeeder first.');
            return;
        }

        // Create portfolio settings
        PortfolioSetting::updateOrCreate(
            ['user_id' => $user->id],
            [
                'meta_title' => 'Ariful Islam | Full-stack Developer',
                'meta_description' => 'Portfolio website showcasing full-stack development work and skills',
                'name' => 'Ariful Islam',
                'title' => 'Full-stack Developer',
                'location' => 'Khulna, Bangladesh',
                'avatar' => 'https://cdn.ariful.dev/avatar-square-bg-white.jpg',
                'email' => '<EMAIL>',
                'phone' => '+8801755900055',
                'working_hours' => 'Saturday - Thursday, 8pm - 2am BST (GMT+6)',
                'available_for_work' => true,
                'badges' => ['Full-stack', 'Laravel', 'React'],
                'bio' => "I'm a full-stack developer passionate about creating impactful web solutions. I use technologies like PHP, Laravel, Livewire, JavaScript, Vue, React, and Alpine.js to build efficient, intuitive web experiences that solve real problems.",
                'focus' => [
                    'Full-Stack Web Development & Implementation',
                    'Crafting Intuitive & Efficient Web Experiences',
                    'Impact-Driven Problem Solving Through Technology'
                ],
                'languages' => [
                    [
                        'name' => 'Bangla',
                        'proficiency' => 'Native',
                        'level' => 100,
                        'flag' => '🇧🇩'
                    ],
                    [
                        'name' => 'English',
                        'proficiency' => 'Fluent',
                        'level' => 70,
                        'flag' => '🇺🇸'
                    ]
                ],
                'interests' => ['Food', 'Bikes', 'Badminton', 'Traveling']
            ]
        );

        // Create experiences
        $experiences = [
            [
                'title' => 'Full-stack Developer',
                'company' => 'bdCoder',
                'period' => 'Feb, 2022 - Sep, 2024',
                'description' => '',
                'achievements' => [],
                'technologies' => ['PHP', 'MySQL', 'Laravel', 'Livewire', 'JavaScript', 'Vue', 'React', 'Alpine.js', 'jQuery', 'SCSS', 'Tailwind'],
                'sort_order' => 1
            ],
            [
                'title' => 'Web Developer / Laravel Developer',
                'company' => 'Rapid IT Ltd.',
                'period' => 'Oct, 2021 - Jan, 2022',
                'description' => '',
                'achievements' => [],
                'technologies' => ['PHP', 'MySQL', 'Laravel', 'JavaScript', 'jQuery', 'HTML', 'CSS', 'Bootstrap'],
                'sort_order' => 2
            ],
            [
                'title' => 'Asst. Jr. Engineer (IT)',
                'company' => 'Faridpur Palli Bidyut Samity',
                'period' => 'Jan, 2020 - Dec, 2020',
                'description' => '',
                'achievements' => [],
                'technologies' => ['Networking', 'Server Management', 'Server Administration', 'Database', 'PL/SQL', 'Oracle'],
                'sort_order' => 3
            ]
        ];

        foreach ($experiences as $experience) {
            Experience::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'title' => $experience['title'],
                    'company' => $experience['company']
                ],
                array_merge($experience, ['user_id' => $user->id])
            );
        }

        // Create skills
        $skills = [
            // Development skills
            ['name' => 'PHP', 'category' => 'development', 'proficiency' => 90, 'sort_order' => 1],
            ['name' => 'MySQL', 'category' => 'development', 'proficiency' => 85, 'sort_order' => 2],
            ['name' => 'Laravel', 'category' => 'development', 'proficiency' => 90, 'sort_order' => 3],
            ['name' => 'Livewire', 'category' => 'development', 'proficiency' => 85, 'sort_order' => 4],
            ['name' => 'JavaScript', 'category' => 'development', 'proficiency' => 80, 'sort_order' => 5],
            ['name' => 'Vue', 'category' => 'development', 'proficiency' => 75, 'sort_order' => 6],
            ['name' => 'React', 'category' => 'development', 'proficiency' => 75, 'sort_order' => 7],
            ['name' => 'Tailwind', 'category' => 'development', 'proficiency' => 85, 'sort_order' => 8],
            ['name' => 'Alpine.js', 'category' => 'development', 'proficiency' => 80, 'sort_order' => 9],
            ['name' => 'jQuery', 'category' => 'development', 'proficiency' => 85, 'sort_order' => 10],
            ['name' => 'SCSS', 'category' => 'development', 'proficiency' => 80, 'sort_order' => 11],
            ['name' => 'Git', 'category' => 'development', 'proficiency' => 85, 'sort_order' => 12],
            ['name' => 'HTML/CSS', 'category' => 'development', 'proficiency' => 90, 'sort_order' => 13],

            // Soft skills
            ['name' => 'Team Leadership', 'category' => 'soft_skills', 'proficiency' => 85, 'sort_order' => 1],
            ['name' => 'Project Management', 'category' => 'soft_skills', 'proficiency' => 80, 'sort_order' => 2],
            ['name' => 'Client Communication', 'category' => 'soft_skills', 'proficiency' => 85, 'sort_order' => 3],
            ['name' => 'Mentoring', 'category' => 'soft_skills', 'proficiency' => 80, 'sort_order' => 4],
            ['name' => 'Presentations', 'category' => 'soft_skills', 'proficiency' => 75, 'sort_order' => 5],
        ];

        foreach ($skills as $skill) {
            Skill::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'name' => $skill['name'],
                    'category' => $skill['category']
                ],
                array_merge($skill, ['user_id' => $user->id])
            );
        }

        // Create social links
        $socialLinks = [
            [
                'platform' => 'GitHub',
                'url' => 'https://github.com/arman-arif',
                'icon' => 'Github',
                'sort_order' => 1
            ],
            [
                'platform' => 'LinkedIn',
                'url' => 'https://linkedin.com/in/armanarif',
                'icon' => 'Linkedin',
                'sort_order' => 2
            ],
            [
                'platform' => 'Twitter',
                'url' => 'https://x.com/arman3472',
                'icon' => 'Twitter',
                'sort_order' => 3
            ],
            [
                'platform' => 'Facebook',
                'url' => 'https://facebook.com/arman3472',
                'icon' => 'Facebook',
                'sort_order' => 4
            ]
        ];

        foreach ($socialLinks as $socialLink) {
            SocialLink::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'platform' => $socialLink['platform']
                ],
                array_merge($socialLink, ['user_id' => $user->id])
            );
        }

        // Create certifications
        $certifications = [
            [
                'name' => 'Get Started with Laravel',
                'issuer' => 'PluralSight',
                'date' => '2021',
                'logo' => 'https://cdn.ariful.dev/logo/pluralsight-192x192.png',
                'sort_order' => 1
            ],
            [
                'name' => 'Web Development',
                'issuer' => 'OpenIT Ltd.',
                'date' => '2019',
                'logo' => 'https://cdn.ariful.dev/logo/logo-openitbd.png',
                'sort_order' => 2
            ]
        ];

        foreach ($certifications as $certification) {
            Certification::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'name' => $certification['name'],
                    'issuer' => $certification['issuer']
                ],
                array_merge($certification, ['user_id' => $user->id])
            );
        }

        // Create education
        $educations = [
            [
                'degree' => 'Computer Science & Engineering',
                'institution' => 'North Western University, Khulna',
                'year' => '2020 (Dropped out)',
                'logo' => 'https://cdn.ariful.dev/logo/nwu-logo.png',
                'sort_order' => 1
            ],
            [
                'degree' => 'Diploma in Computer Technology',
                'institution' => 'Khulna Polytechnic Institute',
                'year' => '2015-2019',
                'logo' => 'https://cdn.ariful.dev/logo/kpi-logo.jpg',
                'sort_order' => 2
            ]
        ];

        foreach ($educations as $education) {
            Education::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'degree' => $education['degree'],
                    'institution' => $education['institution']
                ],
                array_merge($education, ['user_id' => $user->id])
            );
        }

        $this->command->info('Portfolio data seeded successfully!');
    }
}
