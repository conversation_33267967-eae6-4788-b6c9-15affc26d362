<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create a default user for the portfolio
        User::factory()->create([
            'name' => 'Ariful',
            'email' => '<EMAIL>',
            'password' => 'Arm@n27626', // This will be hashed by the factory
            'email_verified_at' => now(),
        ]);

        // Seed portfolio data
        $this->call(PortfolioSeeder::class);
    }
}
