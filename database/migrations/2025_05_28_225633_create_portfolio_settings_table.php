<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('portfolio_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');

            // Meta information
            $table->string('meta_title')->nullable();
            $table->text('meta_description')->nullable();

            // Personal information
            $table->string('name');
            $table->string('title');
            $table->string('location')->nullable();
            $table->string('avatar')->nullable();
            $table->string('email');
            $table->string('phone')->nullable();
            $table->string('working_hours')->nullable();
            $table->boolean('available_for_work')->default(true);
            $table->json('badges')->nullable(); // Array of badges

            // About section
            $table->text('bio')->nullable();
            $table->json('focus')->nullable(); // Array of focus areas
            $table->json('languages')->nullable(); // Array of language objects
            $table->json('interests')->nullable(); // Array of interests

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('portfolio_settings');
    }
};
